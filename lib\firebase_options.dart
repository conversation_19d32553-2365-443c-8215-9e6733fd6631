// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAAEnOFJqFhrNsVNgIADfOW22AWbr3Qs2s',
    appId: '1:611366161595:web:29a4e88dd324717cfe6d9e',
    messagingSenderId: '611366161595',
    projectId: 'shaghalny-173e7',
    authDomain: 'shaghalny-173e7.firebaseapp.com',
    storageBucket: 'shaghalny-173e7.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC_SB19pvLyhzP998LD9eCC1XUdhj3rABU',
    appId: '1:611366161595:android:028ec899a47e93fffe6d9e',
    messagingSenderId: '611366161595',
    projectId: 'shaghalny-173e7',
    storageBucket: 'shaghalny-173e7.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyABXdqg5TYLY-KHY06SfO-PrBYTg0OiNDA',
    appId: '1:611366161595:ios:774a4df43a096040fe6d9e',
    messagingSenderId: '611366161595',
    projectId: 'shaghalny-173e7',
    storageBucket: 'shaghalny-173e7.firebasestorage.app',
    iosBundleId: 'com.example.shoghlany',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyABXdqg5TYLY-KHY06SfO-PrBYTg0OiNDA',
    appId: '1:611366161595:ios:774a4df43a096040fe6d9e',
    messagingSenderId: '611366161595',
    projectId: 'shaghalny-173e7',
    storageBucket: 'shaghalny-173e7.firebasestorage.app',
    iosBundleId: 'com.example.shoghlany',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAAEnOFJqFhrNsVNgIADfOW22AWbr3Qs2s',
    appId: '1:611366161595:web:991cb3de79720274fe6d9e',
    messagingSenderId: '611366161595',
    projectId: 'shaghalny-173e7',
    authDomain: 'shaghalny-173e7.firebaseapp.com',
    storageBucket: 'shaghalny-173e7.firebasestorage.app',
  );
}
