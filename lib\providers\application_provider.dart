import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/application_model.dart';
import '../services/firebase_service.dart';
import 'auth_provider.dart';

final workerApplicationsProvider =
    StreamProvider.family<List<ApplicationModel>, String>((ref, workerId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getWorkerApplicationsStream(workerId).map((snapshot) {
    return snapshot.docs
        .map((doc) {
          final data = doc.data() as Map<String, dynamic>?;
          if (data == null) return null;
          return ApplicationModel.fromMap(data, doc.id);
        })
        .whereType<ApplicationModel>()
        .toList();
  });
});

final jobApplicationsProvider =
    StreamProvider.family<List<ApplicationModel>, String>((ref, jobId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getJobApplicationsStream(jobId).map((snapshot) {
    return snapshot.docs
        .map((doc) {
          final data = doc.data() as Map<String, dynamic>?;
          if (data == null) return null;
          return ApplicationModel.fromMap(data, doc.id);
        })
        .whereType<ApplicationModel>()
        .toList();
  });
});

final applicationProvider = StateNotifierProvider<ApplicationNotifier,
    AsyncValue<List<ApplicationModel>>>((ref) {
  return ApplicationNotifier(ref.watch(firebaseServiceProvider));
});

class ApplicationNotifier
    extends StateNotifier<AsyncValue<List<ApplicationModel>>> {
  final FirebaseService _firebaseService;

  ApplicationNotifier(this._firebaseService)
      : super(const AsyncValue.loading());

  Future<void> applyForJob({
    required String jobId,
    required String applicantId,
  }) async {
    try {
      await _firebaseService.applyForJob(
        jobId: jobId,
        applicantId: applicantId,
      );
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> updateApplicationStatus({
    required String applicationId,
    required String status,
  }) async {
    try {
      await FirebaseFirestore.instance
          .collection('applications')
          .doc(applicationId)
          .update({'status': status});
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}
