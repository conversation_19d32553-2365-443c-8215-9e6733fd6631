import 'package:cloud_firestore/cloud_firestore.dart';

class JobModel {
  final String id;
  final String title;
  final String description;
  final String salary;
  final String location;
  final String postedBy;
  final DateTime createdAt;

  JobModel({
    required this.id,
    required this.title,
    required this.description,
    required this.salary,
    required this.location,
    required this.postedBy,
    required this.createdAt,
  });

  factory JobModel.fromMap(Map<String, dynamic> map, String id) {
    return JobModel(
      id: id,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      salary: map['salary'] ?? '',
      location: map['location'] ?? '',
      postedBy: map['postedBy'] ?? '',
      createdAt: (map['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'salary': salary,
      'location': location,
      'postedBy': postedBy,
      'createdAt': createdAt,
    };
  }
}
