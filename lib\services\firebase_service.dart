import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';

class FirebaseService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Authentication Methods
  Future<UserCredential> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required String phone,
    required String role,
    List<String>? skills,
  }) async {
    try {
      UserCredential userCredential =
          await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Create user document in Firestore
      await _firestore.collection('users').doc(userCredential.user!.uid).set({
        'uid': userCredential.user!.uid,
        'name': name,
        'email': email,
        'phone': phone,
        'role': role,
        'profileImageUrl': '',
        'createdAt': FieldValue.serverTimestamp(),
        if (skills != null) 'skills': skills,
      });

      return userCredential;
    } catch (e) {
      rethrow;
    }
  }

  Future<UserCredential> loginWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    await _auth.signOut();
  }

  Future<void> resetPassword(String email) async {
    await _auth.sendPasswordResetEmail(email: email);
  }

  // User Profile Methods
  Future<void> updateUserProfile({
    required String uid,
    required Map<String, dynamic> data,
  }) async {
    await _firestore.collection('users').doc(uid).update(data);
  }

  Future<String> uploadProfileImage(String uid, File imageFile) async {
    final ref = _storage.ref().child('profile_images/$uid.jpg');
    await ref.putFile(imageFile);
    return await ref.getDownloadURL();
  }

  // Jobs Methods
  Future<void> postJob({
    required String title,
    required String description,
    required String salary,
    required String location,
    required String employerId,
  }) async {
    await _firestore.collection('jobs').add({
      'title': title,
      'description': description,
      'salary': salary,
      'location': location,
      'postedBy': employerId,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  Future<void> applyForJob({
    required String jobId,
    required String applicantId,
  }) async {
    await _firestore.collection('applications').add({
      'jobId': jobId,
      'applicantId': applicantId,
      'status': 'pending',
      'appliedAt': FieldValue.serverTimestamp(),
    });
  }

  // Streams
  Stream<QuerySnapshot> getJobsStream() {
    return _firestore
        .collection('jobs')
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> getEmployerJobsStream(String employerId) {
    return _firestore
        .collection('jobs')
        .where('postedBy', isEqualTo: employerId)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> getWorkerApplicationsStream(String workerId) {
    return _firestore
        .collection('applications')
        .where('applicantId', isEqualTo: workerId)
        .snapshots();
  }

  Stream<QuerySnapshot> getJobApplicationsStream(String jobId) {
    return _firestore
        .collection('applications')
        .where('jobId', isEqualTo: jobId)
        .snapshots();
  }

  // Admin Methods
  Stream<QuerySnapshot> getAllUsersStream() {
    return _firestore.collection('users').snapshots();
  }

  Future<void> deleteUser(String uid) async {
    await _firestore.collection('users').doc(uid).delete();
  }

  Future<void> deleteJob(String jobId) async {
    await _firestore.collection('jobs').doc(jobId).delete();
  }

  Future<void> updateJob({
    required String jobId,
    required String title,
    required String description,
    required String salary,
    required String location,
  }) async {
    await _firestore.collection('jobs').doc(jobId).update({
      'title': title,
      'description': description,
      'salary': salary,
      'location': location,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }
}
