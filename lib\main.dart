import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'firebase_options.dart';
import 'models/job_model.dart';
import 'screens/splash_screen.dart';
import 'screens/welcome_screen.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/forgot_password_screen.dart';
import 'screens/worker/worker_home_screen.dart';
import 'screens/worker/worker_profile_screen.dart';
import 'screens/worker/job_details_screen.dart';
import 'screens/worker/applied_jobs_screen.dart';
import 'screens/employer/employer_home_screen.dart';
import 'screens/employer/post_job_screen.dart';
import 'screens/employer/job_applications_screen.dart';
import 'screens/employer/edit_job_screen.dart';
import 'screens/employer/employer_profile_screen.dart';
import 'theme/app_theme.dart';
import 'providers/auth_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    print("🔥 Firebase Init Error: $e");
  }
  runApp(
    const ProviderScope(
      child: ShoghlanyApp(),
    ),
  );
}

class ShoghlanyApp extends ConsumerWidget {
  const ShoghlanyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    return MaterialApp(
      title: 'Shoghlany',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      home: authState.when(
        data: (user) {
          if (user == null) {
            return const WelcomeScreen();
          }
          return ref.watch(currentUserProvider).when(
                data: (userModel) {
                  if (userModel == null) {
                    return const WelcomeScreen();
                  }
                  switch (userModel.role) {
                    case 'Worker':
                      return const WorkerHomeScreen();
                    case 'Employer':
                      return const EmployerHomeScreen();
                    case 'Admin':
                      // TODO: Add admin home screen
                      return const WelcomeScreen();
                    default:
                      return const WelcomeScreen();
                  }
                },
                loading: () => const SplashScreen(),
                error: (_, __) => const WelcomeScreen(),
              );
        },
        loading: () => const SplashScreen(),
        error: (_, __) => const WelcomeScreen(),
      ),
      routes: {
        '/welcome': (context) => const WelcomeScreen(),
        '/worker-login': (context) => const LoginScreen(role: 'Worker'),
        '/employer-login': (context) => const LoginScreen(role: 'Employer'),
        '/worker-register': (context) => const RegisterScreen(role: 'Worker'),
        '/employer-register': (context) =>
            const RegisterScreen(role: 'Employer'),
        '/forgot-password': (context) => const ForgotPasswordScreen(),
        '/worker-home': (context) => const WorkerHomeScreen(),
        '/worker-profile': (context) => const WorkerProfileScreen(),
        '/worker-job-details': (context) {
          final job = ModalRoute.of(context)!.settings.arguments as JobModel;
          return JobDetailsScreen(job: job);
        },
        '/worker-applied-jobs': (context) => const AppliedJobsScreen(),
        '/employer-home': (context) => const EmployerHomeScreen(),
        '/employer-post-job': (context) => const PostJobScreen(),
        '/employer-job-applications': (context) {
          final jobId = ModalRoute.of(context)!.settings.arguments as String;
          return JobApplicationsScreen(jobId: jobId);
        },
        '/employer-edit-job': (context) {
          final job = ModalRoute.of(context)!.settings.arguments as JobModel;
          return EditJobScreen(job: job);
        },
        '/employer-profile': (context) => const EmployerProfileScreen(),
      },
    );
  }
}
