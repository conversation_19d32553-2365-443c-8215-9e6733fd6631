import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/application_model.dart';
import '../../providers/application_provider.dart';
import '../../providers/auth_provider.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_button.dart';

class AppliedJobsScreen extends ConsumerWidget {
  const AppliedJobsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(currentUserProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Applied Jobs'),
      ),
      body: userAsync.when(
        data: (user) {
          if (user == null) {
            return const Center(
              child: Text('Please log in to view applied jobs.'),
            );
          }

          final appliedJobsAsync =
              ref.watch(workerApplicationsProvider(user.uid));

          return appliedJobsAsync.when(
            data: (applications) {
              if (applications.isEmpty) {
                return const Center(
                  child: Text('You haven\'t applied for any jobs yet.'),
                );
              }
              return ListView.builder(
                padding: const EdgeInsets.all(16.0),
                itemCount: applications.length,
                itemBuilder: (context, index) {
                  final application = applications[index];
                  // TODO: Display job title and application status here
                  return ListTile(
                    title: Text(
                        'Application for Job ID: ${application.jobId}'), // Placeholder
                    subtitle: Text('Status: ${application.status}'),
                  );
                },
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) =>
                Center(child: Text('Error loading applications: $error')),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) =>
            Center(child: Text('Error loading user data: $error')),
      ),
    );
  }
}
