import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/application_model.dart';
import '../../providers/application_provider.dart';
import '../../models/job_model.dart';

class JobApplicationsScreen extends ConsumerWidget {
  final String jobId;

  const JobApplicationsScreen({Key? key, required this.jobId})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final applicationsAsync = ref.watch(jobApplicationsProvider(jobId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Applications'),
      ),
      body: applicationsAsync.when(
        data: (applications) {
          if (applications.isEmpty) {
            return const Center(
              child: Text('No applications for this job yet.'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: applications.length,
            itemBuilder: (context, index) {
              final application = applications[index];
              // TODO: Display applicant details and application status
              return ListTile(
                title: Text(
                    'Applicant ID: ${application.applicantId}'), // Placeholder
                subtitle: Text('Status: ${application.status}'),
                // TODO: Add buttons to accept/reject application
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) =>
            Center(child: Text('Error loading applications: $error')),
      ),
    );
  }
}
