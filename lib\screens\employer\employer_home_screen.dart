import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/auth_provider.dart';
import '../../providers/job_provider.dart';
import '../../models/job_model.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/modern_job_card.dart';
import '../../widgets/modern_states.dart';

class EmployerHomeScreen extends ConsumerWidget {
  const EmployerHomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(currentUserProvider);

    return Scaffold(
      backgroundColor: AppTheme.secondaryColor,
      appBar: AppBar(
        title: Text(
          'My Posted Jobs',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimary,
              ),
        ),
        backgroundColor: AppTheme.surfaceColor,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor: AppTheme.shadowLight,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacing8),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(AppTheme.spacing8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              onPressed: () {
                Navigator.pushNamed(context, '/employer-profile');
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacing16),
            child: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(AppTheme.spacing8),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                ),
                child: const Icon(
                  Icons.logout,
                  color: AppTheme.errorColor,
                  size: 20,
                ),
              ),
              onPressed: () async {
                try {
                  // Show confirmation dialog
                  final shouldLogout = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Logout'),
                      content: const Text('Are you sure you want to logout?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context, false),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.pop(context, true),
                          child: const Text('Logout'),
                        ),
                      ],
                    ),
                  );

                  if (shouldLogout == true) {
                    // Show loading indicator
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Row(
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 16),
                              Text('Logging out...'),
                            ],
                          ),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }

                    await ref.read(authProvider.notifier).logout();

                    if (context.mounted) {
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Logged out successfully'),
                          backgroundColor: Colors.green,
                          duration: Duration(seconds: 1),
                        ),
                      );

                      // Navigate to welcome screen after logout
                      Future.delayed(const Duration(milliseconds: 500), () {
                        if (context.mounted) {
                          Navigator.pushNamedAndRemoveUntil(
                            context,
                            '/welcome',
                            (route) => false,
                          );
                        }
                      });
                    }
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Logout failed: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
            ),
          ),
        ],
      ),
      body: userAsync.when(
        data: (user) {
          if (user == null) return const SizedBox.shrink();

          final jobsAsync = ref.watch(employerJobsProvider(user.uid));

          return jobsAsync.when(
            data: (jobs) {
              if (jobs.isEmpty) {
                return ModernEmptyState(
                  icon: Icons.work_outline,
                  title: 'No Jobs Posted Yet',
                  subtitle:
                      'Start building your team by posting your first job opening. Reach qualified candidates and grow your business.',
                  action: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, '/employer-post-job');
                    },
                    icon: const Icon(Icons.add, size: 20),
                    label: const Text('Post Your First Job'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing24,
                        vertical: AppTheme.spacing16,
                      ),
                    ),
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(AppTheme.spacing16),
                itemCount: jobs.length,
                itemBuilder: (context, index) {
                  final job = jobs[index];
                  return ModernJobCard(
                    job: job,
                    actions: [
                      ModernActionButton(
                        text: 'Applications',
                        icon: Icons.people_outline,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/employer-job-applications',
                            arguments: job.id,
                          );
                        },
                        isOutlined: true,
                      ),
                      const SizedBox(width: AppTheme.spacing8),
                      ModernActionButton(
                        text: 'Edit',
                        icon: Icons.edit_outlined,
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/employer-edit-job',
                            arguments: job,
                          );
                        },
                        color: AppTheme.infoColor,
                        isOutlined: true,
                      ),
                      const SizedBox(width: AppTheme.spacing8),
                      ModernActionButton(
                        text: 'Delete',
                        icon: Icons.delete_outline,
                        onPressed: () => _showDeleteDialog(context, ref, job),
                        color: AppTheme.errorColor,
                        isOutlined: true,
                      ),
                    ],
                  );
                },
              );
            },
            loading: () => const ModernLoadingState(
              message: 'Loading your posted jobs...',
            ),
            error: (error, stackTrace) => ModernErrorState(
              title: 'Unable to Load Jobs',
              subtitle:
                  'There was an error loading your posted jobs. Please check your connection and try again.',
              onRetry: () {
                ref.invalidate(employerJobsProvider);
              },
            ),
          );
        },
        loading: () => const ModernLoadingState(
          message: 'Loading your account...',
        ),
        error: (error, stackTrace) => ModernErrorState(
          title: 'Account Error',
          subtitle:
              'Unable to load your account information. Please try again.',
          onRetry: () {
            ref.invalidate(currentUserProvider);
          },
        ),
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          onPressed: () {
            Navigator.pushNamed(context, '/employer-post-job');
          },
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          icon: const Icon(Icons.add, size: 24),
          label: const Text(
            'Post Job',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, JobModel job) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacing8),
              decoration: BoxDecoration(
                color: AppTheme.errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.warning_outlined,
                color: AppTheme.errorColor,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacing12),
            const Text('Delete Job'),
          ],
        ),
        content: Text(
          'Are you sure you want to delete "${job.title}"? This action cannot be undone.',
          style: TextStyle(
            color: AppTheme.textSecondary,
            height: 1.4,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await ref.read(jobProvider.notifier).deleteJob(job.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Job deleted successfully'),
                      backgroundColor: AppTheme.successColor,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppTheme.radiusSmall),
                      ),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: ${e.toString()}'),
                      backgroundColor: AppTheme.errorColor,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppTheme.radiusSmall),
                      ),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
