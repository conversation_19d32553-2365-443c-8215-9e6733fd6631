import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../services/firebase_service.dart';

final firebaseServiceProvider = Provider<FirebaseService>((ref) {
  return FirebaseService();
});

final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return null;

  try {
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .get();

    if (!doc.exists) return null;
    return UserModel.fromMap(doc.data()!);
  } catch (e) {
    print('Error fetching user data: $e');
    return null;
  }
});

final authProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<UserModel?>>((ref) {
  return AuthNotifier(ref.watch(firebaseServiceProvider));
});

class AuthNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final FirebaseService _firebaseService;

  AuthNotifier(this._firebaseService) : super(const AsyncValue.loading()) {
    _init();
  }

  Future<void> _init() async {
    FirebaseAuth.instance.authStateChanges().listen((user) async {
      if (user == null) {
        state = const AsyncValue.data(null);
        return;
      }

      try {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (!doc.exists) {
          state = const AsyncValue.data(null);
          return;
        }

        state = AsyncValue.data(UserModel.fromMap(doc.data()!));
      } catch (e, st) {
        print('Error in auth state change: $e');
        state = AsyncValue.error(e, st);
      }
    });
  }

  Future<UserModel> register({
    required String email,
    required String password,
    required String name,
    required String phone,
    required String role,
    List<String>? skills,
  }) async {
    try {
      state = const AsyncValue.loading();

      // Register with Firebase Auth
      final userCredential =
          await _firebaseService.registerWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
        phone: phone,
        role: role,
        skills: skills,
      );

      // Fetch the created user document
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userCredential.user!.uid)
          .get();

      if (!doc.exists) {
        throw Exception('User document not found after registration');
      }

      final userModel = UserModel.fromMap(doc.data()!);
      state = AsyncValue.data(userModel);
      return userModel;
    } catch (e) {
      print('Registration error: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<UserModel> login({
    required String email,
    required String password,
  }) async {
    try {
      state = const AsyncValue.loading();

      // Login with Firebase Auth
      await _firebaseService.loginWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Fetch user document
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User not found after login');

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!doc.exists) {
        throw Exception('User document not found');
      }

      final userModel = UserModel.fromMap(doc.data()!);
      state = AsyncValue.data(userModel);
      return userModel;
    } catch (e) {
      print('Login error: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await _firebaseService.logout();
      state = const AsyncValue.data(null);
    } catch (e, st) {
      print('Logout error: $e');
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _firebaseService.resetPassword(email);
    } catch (e, st) {
      print('Password reset error: $e');
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}
