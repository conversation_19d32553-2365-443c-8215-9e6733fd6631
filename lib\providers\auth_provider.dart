/*
 * مقدم خدمات المصادقة - يدير عمليات تسجيل الدخول والخروج وإدارة المستخدمين
 *
 * هذا الملف يحتوي على:
 * - مقد<PERSON>ي خدمات Firebase Auth و Firestore
 * - مراقبة حالة المصادقة
 * - جلب بيانات المستخدم الحالي
 * - عمليات تسجيل الدخول والتسجيل وتسجيل الخروج
 *
 * المطور: فريق شغلاني
 * التاريخ: 2024
 */

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../services/firebase_service.dart';

/// مقدم خدمة Firebase Service - يوفر مثيل خدمة Firebase المخصصة
final firebaseServiceProvider = Provider<FirebaseService>((ref) {
  return FirebaseService();
});

/// مقدم خدمة مراقبة حالة المصادقة - يراقب تغييرات حالة تسجيل الدخول
final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

/// مقدم خدمة بيانات المستخدم الحالي - يجلب بيانات المستخدم من Firestore
final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  // الحصول على المستخدم الحالي من Firebase Auth
  final user = FirebaseAuth.instance.currentUser;
  if (user == null) return null; // إذا لم يكن هناك مستخدم مسجل دخول

  try {
    // جلب وثيقة المستخدم من Firestore
    final doc = await FirebaseFirestore.instance
        .collection('users') // مجموعة المستخدمين
        .doc(user.uid) // وثيقة المستخدم بمعرفه الفريد
        .get();

    // إذا لم تكن الوثيقة موجودة
    if (!doc.exists) return null;

    // تحويل البيانات إلى نموذج مستخدم
    return UserModel.fromMap(doc.data()!);
  } catch (e) {
    print('خطأ في جلب بيانات المستخدم: $e');
    return null;
  }
});

/// مقدم خدمة المصادقة الرئيسي - يدير جميع عمليات المصادقة
final authProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<UserModel?>>((ref) {
  return AuthNotifier(ref.watch(firebaseServiceProvider));
});

/// فئة إدارة حالة المصادقة - تدير جميع عمليات المصادقة وحالة المستخدم
class AuthNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  final FirebaseService _firebaseService;

  /// منشئ الفئة - يبدأ بحالة التحميل ويهيئ مراقبة حالة المصادقة
  AuthNotifier(this._firebaseService) : super(const AsyncValue.loading()) {
    _init();
  }

  /// تهيئة مراقبة حالة المصادقة
  /// يراقب تغييرات حالة تسجيل الدخول ويحدث حالة التطبيق تبعاً لذلك
  Future<void> _init() async {
    FirebaseAuth.instance.authStateChanges().listen((user) async {
      // إذا لم يكن هناك مستخدم مسجل دخول
      if (user == null) {
        state = const AsyncValue.data(null);
        return;
      }

      try {
        // جلب بيانات المستخدم من Firestore
        final doc = await FirebaseFirestore.instance
            .collection('users') // مجموعة المستخدمين
            .doc(user.uid) // وثيقة المستخدم
            .get();

        // إذا لم تكن وثيقة المستخدم موجودة
        if (!doc.exists) {
          state = const AsyncValue.data(null);
          return;
        }

        // تحديث الحالة ببيانات المستخدم
        state = AsyncValue.data(UserModel.fromMap(doc.data()!));
      } catch (e, st) {
        print('خطأ في تغيير حالة المصادقة: $e');
        state = AsyncValue.error(e, st);
      }
    });
  }

  /// تسجيل مستخدم جديد
  /// يقوم بإنشاء حساب جديد في Firebase Auth وحفظ بيانات المستخدم في Firestore
  ///
  /// المعاملات:
  /// - email: البريد الإلكتروني للمستخدم
  /// - password: كلمة المرور
  /// - name: اسم المستخدم
  /// - phone: رقم الهاتف
  /// - role: نوع المستخدم (Worker أو Employer)
  /// - skills: المهارات (اختيارية للباحثين عن العمل)
  ///
  /// يعيد: نموذج المستخدم المُنشأ
  Future<UserModel> register({
    required String email,
    required String password,
    required String name,
    required String phone,
    required String role,
    List<String>? skills,
  }) async {
    try {
      // تعيين حالة التحميل
      state = const AsyncValue.loading();

      // التسجيل في Firebase Auth وحفظ البيانات في Firestore
      final userCredential =
          await _firebaseService.registerWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
        phone: phone,
        role: role,
        skills: skills,
      );

      // جلب وثيقة المستخدم المُنشأة من Firestore
      final doc = await FirebaseFirestore.instance
          .collection('users') // مجموعة المستخدمين
          .doc(userCredential.user!.uid) // وثيقة المستخدم الجديد
          .get();

      // التحقق من وجود الوثيقة
      if (!doc.exists) {
        throw Exception('لم يتم العثور على وثيقة المستخدم بعد التسجيل');
      }

      // تحويل البيانات إلى نموذج مستخدم وتحديث الحالة
      final userModel = UserModel.fromMap(doc.data()!);
      state = AsyncValue.data(userModel);
      return userModel;
    } catch (e) {
      print('خطأ في التسجيل: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow; // إعادة رمي الخطأ للتعامل معه في الواجهة
    }
  }

  Future<UserModel> login({
    required String email,
    required String password,
  }) async {
    try {
      state = const AsyncValue.loading();

      // Login with Firebase Auth
      await _firebaseService.loginWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Fetch user document
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User not found after login');

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (!doc.exists) {
        throw Exception('User document not found');
      }

      final userModel = UserModel.fromMap(doc.data()!);
      state = AsyncValue.data(userModel);
      return userModel;
    } catch (e) {
      print('Login error: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await _firebaseService.logout();
      state = const AsyncValue.data(null);
    } catch (e, st) {
      print('Logout error: $e');
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _firebaseService.resetPassword(email);
    } catch (e, st) {
      print('Password reset error: $e');
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}
