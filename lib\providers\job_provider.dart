import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/job_model.dart';
import '../services/firebase_service.dart';
import 'auth_provider.dart';

final jobsProvider = StreamProvider<List<JobModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getJobsStream().map((snapshot) {
    return snapshot.docs
        .map((doc) {
          final data = doc.data() as Map<String, dynamic>?;
          if (data == null) return null;
          return JobModel.fromMap(data, doc.id);
        })
        .whereType<JobModel>()
        .toList();
  });
});

final employerJobsProvider =
    StreamProvider.family<List<JobModel>, String>((ref, employerId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getEmployerJobsStream(employerId).map((snapshot) {
    return snapshot.docs
        .map((doc) {
          final data = doc.data() as Map<String, dynamic>?;
          if (data == null) return null;
          return JobModel.fromMap(data, doc.id);
        })
        .whereType<JobModel>()
        .toList();
  });
});

final jobProvider =
    StateNotifierProvider<JobNotifier, AsyncValue<List<JobModel>>>((ref) {
  return JobNotifier(ref.watch(firebaseServiceProvider));
});

class JobNotifier extends StateNotifier<AsyncValue<List<JobModel>>> {
  final FirebaseService _firebaseService;

  JobNotifier(this._firebaseService) : super(const AsyncValue.loading()) {
    _init();
  }

  Future<void> _init() async {
    try {
      _firebaseService.getJobsStream().listen((snapshot) {
        final jobs = snapshot.docs
            .map((doc) {
              final data = doc.data() as Map<String, dynamic>?;
              if (data == null) return null;
              return JobModel.fromMap(data, doc.id);
            })
            .whereType<JobModel>()
            .toList();
        state = AsyncValue.data(jobs);
      });
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> postJob({
    required String title,
    required String description,
    required String salary,
    required String location,
    required String employerId,
  }) async {
    try {
      await _firebaseService.postJob(
        title: title,
        description: description,
        salary: salary,
        location: location,
        employerId: employerId,
      );
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> deleteJob(String jobId) async {
    try {
      await _firebaseService.deleteJob(jobId);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }

  Future<void> updateJob({
    required String jobId,
    required String title,
    required String description,
    required String salary,
    required String location,
  }) async {
    try {
      await _firebaseService.updateJob(
        jobId: jobId,
        title: title,
        description: description,
        salary: salary,
        location: location,
      );
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      rethrow;
    }
  }
}
